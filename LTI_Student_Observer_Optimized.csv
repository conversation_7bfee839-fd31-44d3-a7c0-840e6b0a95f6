Story,Task,Description,Dev Hours,Owner,QA Hours
Milestone 1: Foundation Setup,,,,,
Initial LTI & Rails Setup,LTI 1.3 & Rails Configuration,"Setup PandaPal gem for LTI 1.3 authentication with Canvas. Initialize Rails 7 application with required gems (pandapal, canvas_sync, sidekiq, redis, pg). Configure OIDC login flow and basic LTI launch controllers.",10,,2
Frontend & Database Setup,React & Database Foundation,"Setup React 18.3.1 with <PERSON>hak<PERSON>cker and Instructure UI components. Design and implement core database schema (observer_links, canvas_users, sync_logs). Create Rails migrations with proper indexes.",8,,2
Canvas API Integration,Canvas API Client & Authentication,"Setup Canvas API client using canvas_sync gem. Configure OAuth2 authentication, token refresh, and rate limiting. Create base API service classes for Canvas integration.",4,,1
Milestone 2: Student Lists & Observer Links,,,,,
Student List Implementation,Course & Account Student Lists,"Implement APIs to fetch course enrollments and sub-account students with pagination (50 per page) and search functionality. Create React components for student lists with sorting and filtering.",12,,3
Observer Link Core Functionality,Observer Link Creation & Management,"Implement Canvas API integration for creating/deleting temporary observer links. Create database models, confirmation dialogs, link viewing, manual termination, and 1-hour auto-expiration logic.",12,,3
Link Renewal & Multi-Instance Sync,Renewal System & Cross-Instance Sync,"Implement one-time link renewal functionality with timer reset. Setup data synchronization across 4 Canvas instances using shared database with conflict resolution.",8,,2
Milestone 3: Background Jobs & Calendar,,,,,
Background Processing Setup,Sidekiq & Data Sync Jobs,"Configure Sidekiq with Redis for background jobs. Implement 6-hour Canvas data sync jobs and automatic observer link cleanup with error recovery and retry logic.",10,,2
Calendar Integration,Student Calendar Viewing,"Implement API to fetch student calendar data (personal + course calendars). Build read-only calendar interface with filtering logic (9 calendar limit) and navigation controls using Instructure UI.",12,,3
Milestone 4: UI/UX & Testing,,,,,
UI/UX Implementation,Complete User Interface,"Integrate Instructure Design System throughout application. Implement launch point interfaces, error handling, loading states, and responsive design for mobile/tablet compatibility.",10,,2
Testing & Quality Assurance,Comprehensive Testing Suite,"Implement API testing with RSpec, React component testing with Jest, and end-to-end testing with Cypress. Cover all user workflows and error scenarios.",8,,
Milestone 5: Deployment & Documentation,,,,,
Deployment & Documentation,Production Setup & Documentation,"Configure production environment for multi-instance deployment. Install LTI tool in all 4 Canvas instances. Create technical documentation and deployment guides.",6,,1
,,,,,
Total Hours Summary,,,,,
Development Hours,,Total: 100 hours,,,
QA Hours,,Total: 21 hours,,,
Grand Total,,121 hours,,,
,,,,,
Optimized for 80 Hours,,,,,
If 80 hours is strict limit,,"Reduce testing to 4 hours, combine some UI tasks, minimal documentation",80,,15
