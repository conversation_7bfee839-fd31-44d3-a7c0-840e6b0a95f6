import { useEffect, useState } from 'react';
import { Overlay } from '@instructure/ui-overlays';
import { View } from '@instructure/ui-view';
import { Player } from '@lottiefiles/react-lottie-player'; // Add this import

const CelebrationOverlay = ({
  open,
  onClose,
  gifSrc,
  videoSrc,
  lottieSrc, // Add this prop for lottie file
  audioSrc,
  playAudio = false,
  duration = 5000,
  altText = "Fireworks Animation"
}) => {
  const [show, setShow] = useState(open);

  useEffect(() => {
    if (open) {
      setShow(true);
      if (playAudio && audioSrc) {
        const audio = new Audio(audioSrc);
        audio.play().catch(() => {});
      }
      const timer = setTimeout(() => {
        setShow(false);
        if (onClose) onClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [open, playAudio, audioSrc, duration, onClose]);

  if (!show) return null;

  const renderLottie = () => (
    <View as="div" className="fireworks-lottie-overlay">
      <Player
        src={lottieSrc}
        autoplay
        loop
        style={{ width: '100%', height: '100%' }}
      />
    </View>
  );

  const renderVideo = () => (
    <View as="div" className="fireworks-video-overlay">
      <video
        src={videoSrc}
        autoPlay
        loop
        muted
        playsInline
        className="fireworks-video"
        style={{ width: '100%', height: '100%' }}
        aria-label={altText}
      />
    </View>
  );

  const renderGif = () => (
    <View as="div" className="fireworks-gif-overlay">
      <img
        src={gifSrc}
        alt={altText}
        style={{ width: '100%', height: '100%' }}
        className="fireworks-gif"
      />
    </View>
  );

  return (
    <Overlay
      open={show}
      onDismiss={() => {
        setShow(false);
        if (onClose) onClose();
      }}
      shouldCloseOnEscape={false}
      defaultFocusElement={() => document.body}
      label='Celebration Overlay'
    >
      {lottieSrc ? renderLottie() : videoSrc ? renderVideo() : renderGif()}
    </Overlay>
  );
};

export default CelebrationOverlay;
