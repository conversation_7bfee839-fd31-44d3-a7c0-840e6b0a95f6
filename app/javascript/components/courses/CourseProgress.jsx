import { useEffect, useState } from 'react';
import { useParams } from "react-router";

import { ProgressCircle } from '@instructure/ui-progress';
import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { IconCoursesLine, IconFilterLine, IconArrowOpenDownLine } from '@instructure/ui-icons';
import { Metric } from '@instructure/ui-metric';
import { Popover } from '@instructure/ui-popover';
import { Checkbox } from '@instructure/ui-checkbox';
import { Button } from '@instructure/ui-buttons';

import * as API from "../../utils/api";
import CourseModules from './CourseModules';
import CourseProgressCircle from './CourseProgressCircle';
import { CounterCircle } from '../../shared/components/UtilUI';
import CelebrationOverlay from '../../shared/components/CelebrationOverlay';
import { useTheme } from '../../shared/contexts/ThemeContext';

const CourseProgress = (props) => {
  const { studentId, courseId, orgShardId } = props;
  const [course, setCourse] = useState({});
  const [omittedValues, setOmittedValues] = useState([]);
  const [showFireworks, setShowFireworks] = useState(false);
  const [showingFilter, setShowingFilter] = useState(false);
  const { themeAssets: contextThemeAssets } = useTheme();
  const userConfig = {
    canvasId: window.ENV.user_config.canvas_user_id,
    audioEnabled: window.ENV.user_config.audio_enabled,
    homePage: window.ENV.user_config.home_page,
    theme: window.ENV.user_config.theme
  }

  useEffect(() => {
    if(omittedValues == []){
      getCourseDetail();
    }
  }, [courseId, studentId]);

  const getCourseDetail = () => {
    API.getStudentCourseProgress(studentId, courseId, {filters: omittedValues, org_shard_id: orgShardId})
      .then((response) => response.data)
      .then((response) => {
        setCourse(response);
        setShowFireworks(response.show_fireworks);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const updateFilterValues = (valueToUpdate) => {
    const index = omittedValues.indexOf(valueToUpdate);
    if (index === -1) {
      setOmittedValues([...omittedValues, valueToUpdate])
    } else {
      setOmittedValues([...omittedValues.slice(0, index), ...omittedValues.slice(index + 1)])
    }
    setShowingFilter(false)
  }

  const handleOnCloseFireworks = () => {
    setShowFireworks(false);
    API.updateStudentCelebrationEvent(studentId, {course_id: courseId})
      .then((response) => {
        console.log('Celebration event updated successfully');
      })
      .catch((error) => {
        console.log('Error updating celebration event:', error);
      });
  }

  const renderCourseScore = () => {
    const current_score = course.current_score ? course.current_score + '%' : '- -';
    const grade = course.current_letter_grade ? course.current_letter_grade : '- -';
    const colorCode = course.color_code ? course.color_code : '#BF32A4';

    return (
      <View as="div">
        <View background="secondary" display="inline-block" borderRadius="medium none none medium">
          <Metric
            renderLabel="Current Score"
            renderValue={current_score}
            {...props}
            themeOverride={{
              valueColor: colorCode,
              labelColor: colorCode
            }}
          />
        </View>
        <View background="alert"
          display="inline-block"
          borderRadius="none medium medium none"
          {...props}
          themeOverride={{
            backgroundAlert: colorCode
          }}
        >
          <Metric
            renderLabel="Grade"
            renderValue={grade}
            {...props}
            themeOverride={{
              valueColor: '#FFFFFF',
              labelColor: '#FFFFFF'
            }}
          />
        </View>
      </View>
    )
  }

  const renderCourseStats = (modules) => {
    let totalItems = 0;
    let masteredItems = 0;
    let pasDueItems = 0;

    if (modules) {
      modules.forEach(mod => {
        totalItems += Number(mod['items_count']) || 0;
        masteredItems += Number(mod['mastered']) || 0;
        pasDueItems += Number(mod['past due']) || 0;
      })
    }

    const CustomColorText = ({ children }) => (
      <span style={{ color: '#6A7883', fontSize: '1.25rem' }}>{children}</span>
    );

    return (
            <View as="div">
              <Text size="large" weight="bold">
                <Flex>
                  <Flex.Item size="30px"><IconCoursesLine /></Flex.Item>
                  <Flex.Item style={{ fontSize: '1.375' }}>{course.name}</Flex.Item>
                </Flex>
              </Text>
              <Text size="small">
                <Flex>
                  <Flex.Item size="30px"></Flex.Item>
                  <Flex.Item>{totalItems} Items</Flex.Item>
                  <Flex.Item padding="none x-small"> <CustomColorText>|</CustomColorText></Flex.Item>
                  <Flex.Item>
                    <CounterCircle
                      counts={masteredItems}
                      borderColor="success"
                      themeOverride={{
                        color: '#0B874B',
                        backgroundPrimary: '#ffeb3b'
                      }}
                    /> Mastered
                  </Flex.Item>
                  <Flex.Item padding="none x-small"><CustomColorText>|</CustomColorText></Flex.Item>
                  <Flex.Item>
                    <CounterCircle
                      counts={pasDueItems}
                      borderColor="danger"
                      themeOverride={{
                        color: '#E0061F'
                      }}
                    /> Past Due
                  </Flex.Item>
                </Flex>
              </Text>
            </View>
    )
  }

  const renderFilterButton = () => {
    return (
      <Button
        withBackground={false}
        themeOverride={{
          borderStyle: 'none'
        }}
        >
        <Text>
          <View className='header-text'>
            <IconFilterLine/> Filter <IconArrowOpenDownLine/>
          </View>
        </Text>
      </Button>
    )
  }

  const renderFilters = () => {
    return (
      <Popover
        renderTrigger={ renderFilterButton() }
        isShowingContent={showingFilter}
        on="click"
        onShowContent={(e) => {setShowingFilter(true)}}
        onHideContent={(e, {documentClick}) => {setShowingFilter(false)}}
        placement='bottom end'
        shouldCloseOnDocumentClick={true}
        shouldContainFocus={false}
        shouldFocusContentOnTriggerBlur={true}
      >
        <View display="block">
          <View
            as="div"
            padding="small"
            borderWidth="small"
            themeOverride={{
              borderWidthSmall: '0.05rem'
            }}
          >
            <Text size="small" weight="bold">Filter Items Displayed by Status</Text>
          </View>
          <View
            as="div"
            padding="small"
            borderWidth="small"
            themeOverride={{
              borderWidthSmall: '0.05rem'
            }}
          >
            <View as="div">
              <Checkbox
                label={<Text size="small">Completed</Text>}
                key="completed"
                value="completed"
                onChange={(event) => {updateFilterValues(event.target.value)}}
                checked={omittedValues.indexOf('completed') == -1}
              />
            </View>
            <View as="div" padding="small none">
              <Checkbox
                label={<Text size="small">Completed: Mastered</Text>}
                key="master"
                value="master"
                onChange={(event) => {updateFilterValues(event.target.value)}}
                checked={omittedValues.indexOf('master') == -1}
              />
            </View>
            <View as="div">
              <Checkbox
                label={<Text size="small">Completed: Not Mastered</Text>}
                key="not_master"
                value="not_master"
                onChange={(event) => {updateFilterValues(event.target.value)}}
                checked={omittedValues.indexOf('not_master') == -1}
              />
            </View>
            <View as="div" padding="small none">
              <Checkbox
                label={<Text size="small">Not Completed</Text>}
                key="not_completed"
                value="not_completed"
                onChange={(event) => {updateFilterValues(event.target.value)}}
                checked={omittedValues.indexOf('not_completed') == -1}
              />
            </View>
            <View as="div">
              <Checkbox
                label={<Text size="small">Not Completed: Past Due</Text>}
                key="not_completed_past_due"
                value="not_completed_past_due"
                onChange={(event) => {updateFilterValues(event.target.value)}}
                checked={omittedValues.indexOf('not_completed_past_due') == -1}
              />
            </View>
          </View>
        </View>
      </Popover>
    )
  }

  const renderCourseModulesCount = () => {
    let count = 0
    if (course.modules) {
      count = course.modules.length
    }
    return (
      <View>
        <Text size="small">
          <Text weight="bold">Course Modules</Text> ({count})
        </Text>
      </View>
    )
  }

  const renderCourseDetails = () => {
    return (
      <View as="div" padding="x-small" className='header-text'>
        <Flex>
          <Flex.Item padding="x-small none" size="60%" textAlign="start">
            { course && renderCourseStats(course.modules) }
          </Flex.Item>
          <Flex.Item padding="x-small" size="20%">
            <CourseProgressCircle course={course} />
          </Flex.Item>
          <Flex.Item padding="x-small none" shouldShrink shouldGrow textAlign="end">
            { renderCourseScore() }
          </Flex.Item>
        </Flex>
        <Flex>
          <Flex.Item shouldShrink shouldGrow textAlign="start">
            { renderCourseModulesCount() }
          </Flex.Item>
          <Flex.Item textAlign="end">
            { renderFilters() }
          </Flex.Item>
        </Flex>
      </View>
    )
  }

  useEffect(() =>{
    getCourseDetail();
  },[omittedValues])

  const renderCourseModules = () => {
    return (
      <CourseModules course={course} studentId={studentId}/>
    )
  }

  return(
    <View>
      <Flex direction="column">
        <Flex.Item>
          { course && renderCourseDetails() }
        </Flex.Item>
        <Flex.Item>
          { course && renderCourseModules() }
        </Flex.Item>
      </Flex>
      {showFireworks && (
        <CelebrationOverlay
        open={showFireworks}
        onClose={() => handleOnCloseFireworks()}
        lottieSrc={contextThemeAssets?.elements?.celebrationVideo}
        audioSrc={contextThemeAssets?.elements?.celebrationAudio}
        playAudio={userConfig?.audioEnabled}
        />
      )}
    </View>
  )
}

export default CourseProgress
