Story,Task,Description,Dev Hours,Owner,QA Hours
Milestone 1: Foundation Setup,,,,,
Initial LTI Setup,LTI 1.3 Configuration with PandaPal,"Setup PandaPal gem for LTI 1.3 authentication with Canvas. Configure OIDC login flow, JWT validation, and deep linking support. Create LTI launch controllers for course_navigation and account_navigation placements.",8,,2
Rails & React Foundation,Rails 7 & React 18 Setup,"Initialize Rails 7 application with required gems (pandapal, canvas_sync, sidekiq, redis, pg). Setup React 18.3.1 with <PERSON>hak<PERSON>cker and Instructure UI components. Configure basic routing and app structure.",10,,2
Database & API Setup,Database Schema & Canvas API,"Design and implement core database schema (observer_links, canvas_users, sync_logs). Setup Canvas API client with OAuth2 authentication and rate limiting. Create base API service classes.",6,,1
Milestone 2: Core Observer Functionality,,,,,
Course Level Student Lists,Course Student API & UI Implementation,"Implement API to fetch course enrollments with pagination (50 per page) and alphabetical sorting by sortable name. Create React component to display student list with SIS IDs and selection functionality for observer link creation.",8,,2
Account Level Student Access,Account Student Lists with Search,"Implement API to fetch sub-account students with search functionality (3+ character filter). Create React component with search input, debouncing, filtered results, and pagination for large datasets.",6,,2
Observer Link Management,Temporary Observer Link Creation & Control,"Implement Canvas API integration to create/delete temporary observer links. Create database model to store link metadata with timestamps. Build confirmation dialog, link viewing, manual termination, and 1-hour auto-expiration.",12,,3
Link Renewal & Sync,Renewal System & Multi-Instance Sync,"Implement one-time link renewal functionality with timer reset. Setup data synchronization across 4 Canvas instances using shared database. Handle conflict resolution and cross-instance data consistency.",8,,2
Milestone 3: Background Processing,,,,,
Background Jobs Setup,Sidekiq Configuration & Data Sync,"Configure Sidekiq with Redis for background job processing. Implement 6-hour Canvas data sync jobs for users, courses, and enrollments. Include error recovery and progress tracking mechanisms.",8,,2
Observer Link Cleanup,Automatic Link Expiration Jobs,"Implement background jobs for automatic cleanup of expired observer links. Handle Canvas API calls to remove links, update database status, and cleanup orphaned records with comprehensive retry logic.",4,,1
Milestone 4: Calendar Integration,,,,,
Calendar Data & Display,Student Calendar Implementation,"Implement API to fetch student calendar data (personal + course calendars). Build calendar filtering logic with 9 calendar limit and default visibility rules. Create user preference system for calendar display.",10,,2
Calendar UI Component,Read-only Calendar Interface,"Build Canvas-like calendar interface using Instructure UI components. Implement read-only restrictions, responsive design, navigation controls (month/week/day views), and event details modal.",8,,2
Milestone 5: UI/UX & Deployment,,,,,
UI/UX Implementation,Complete User Interface & Experience,"Integrate Instructure Design System throughout application. Implement launch point interfaces, comprehensive error handling, loading states, and responsive design for mobile/tablet compatibility.",10,,2
Testing & Deployment,Testing Suite & Production Setup,"Implement essential API testing with RSpec and React component testing. Configure production environment for multi-instance deployment. Install LTI tool in all 4 Canvas instances with proper placements.",6,,1
Documentation,Technical Documentation & Guides,"Create essential technical documentation including API documentation, deployment guide, and troubleshooting manual for administrators and developers.",2,,
,,,,,
Total Hours Summary,,,,,
Development Hours,,Total: 106 hours,,,
QA Hours,,Total: 22 hours,,,
Grand Total,,128 hours,,,
,,,,,
80-Hour Optimized Version,,,,,
Reduce Testing,Minimal Testing Approach,"Focus on critical path testing only, reduce comprehensive test coverage",4,,1
Reduce Documentation,Essential Documentation Only,"Create only critical deployment and API documentation",1,,
Reduce Calendar Features,Basic Calendar Implementation,"Implement basic calendar viewing without advanced filtering",6,,1
Adjusted Total for 80 Hours,,Optimized breakdown to meet 80-hour constraint,80,,15
