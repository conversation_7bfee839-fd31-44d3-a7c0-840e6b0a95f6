# frozen_string_literal: true

# #
# AUTO GENERATED LIVE EVENT
# This was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#
# LiveEvent message formats can be found at https://canvas.instructure.com/doc/api/file.live_events.html
# In the general case, LiveEvent message content should not be trusted - it is highly possible that events may
# arrive out of order. Most of the CanvasSync LiveEvent templates solve this by always fetching the object from the API.
#

module LiveEvents
  class EnrollmentEvent < CanvasSync::LiveEvents::BaseHandler
    attr_accessor :enrollment

    def process
      @enrollment = Enrollment.where(canvas_id: local_canvas_id(payload[:enrollment_id])).first_or_initialize
      was_new_enrollment = @enrollment.new_record?
      enrollment.canvas_course_id = ensure_course.canvas_id
      enrollment.sync_from_api

      if enrollment.user.nil?
        u = User.new(canvas_id: enrollment.canvas_user_id)
        u.sync_from_api
        # enrollment.reload to get the user association if sync_from_api created it
        enrollment.reload
      end

      post_enrollment_create_submissions(enrollment) if was_new_enrollment && enrollment.user.present?
    end

    private

    def ensure_course
      course = Course.where(canvas_id: local_canvas_id(payload[:course_id])).first_or_initialize
      return course unless course.new_record?

      course.sync_from_api
      course
    end

    # Live events will use a canvas global ID (cross shard) for any ID's provided. This method will return the local ID.
    def local_canvas_id(id)
      id.to_i % 10_000_000_000_000
    end

    # This method creates initial submissions for the enrollment if it is a new record.
    def post_enrollment_create_submissions(enrollment)
      return unless enrollment.base_role_type == 'StudentEnrollment' # Only create submissions for student enrollments

      student_submissions = canvas_sync_client.course_submissions(enrollment.canvas_course_id, { student_ids: [enrollment.canvas_user_id] })
      student_submissions.each do |submission_data|
        submission = Submission.where(canvas_id: submission_data['id']).first_or_initialize
        submission.canvas_course_id = enrollment.canvas_course_id
        submission.canvas_assignment_id = submission_data['assignment_id']
        submission.canvas_user_id = submission_data['user_id']
        submission.workflow_state = submission_data['workflow_state']
        submission.due_at = submission_data['cached_due_date']
        submission.save!
      end
      Rails.logger.info("EnrollmentEvent:: Initial submissions created for enrollment: #{enrollment.canvas_id}")
    rescue StandardError => e
      Rails.logger.error("EnrollmentEvent:: Error creating initial submissions for enrollment #{enrollment.canvas_id}: #{e.message}")
    end
  end
end
