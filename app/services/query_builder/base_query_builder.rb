# frozen_string_literal: true

module QueryBuilder
  class BaseQueryBuilder
    attr_accessor :user_id, :course_id, :filters, :organization_id, :organization_name,
                  :organization_base_url, :organization_shard_id, :request_org

    def initialize(options = {})
      @user_id = options[:canvas_user_id].to_i
      @course_id = options[:canvas_course_id].to_i
      @filters = options[:filters] || {}
      return unless options[:organization]

      @organization_id = options[:organization].id
      @organization_name = options[:organization].name
      @organization_base_url = options[:organization].base_url
      # Fallback to 1 in case the org doesn't have a canvas shard id
      @organization_shard_id = options[:organization].canvas_shard_id || 1
      @request_org = options[:request_org]
    end

    def user_courses_with_requirement_counts(canvas_course_ids)
      <<~SQL.squish
        WITH
          user_courses AS (#{user_courses_with_requirements(canvas_course_ids)}),
          course_req_counts AS (#{course_requiment_counts})
          SELECT * FROM course_req_counts
      SQL
    end

    def course_requiment_counts
      <<~SQL.squish
        SELECT canvas_course_id,
          count(*) as requirement_count,
          sum(
            case
              when requirement_status = 'completed'
              then 1 else 0
            end
          ) as requirement_completed_count,
          sum(
            case
              when COALESCE(todo_date, due_at, lock_at) < NOW() then 1 else 0
            end
          ) as past_due_requirements_count,
          sum(
            case
              when todo_date IS NOT NULL or due_at IS NOT NULL or lock_at IS NOT NULL then 1 else 0
            end
          ) as requirements_with_due_date_count
        FROM user_courses
        GROUP BY canvas_course_id
      SQL
    end

    def user_courses_with_requirements(canvas_course_ids)
      <<~SQL.squish
        SELECT courses.canvas_id AS canvas_course_id, cmp.requirement_type AS requirement_type, cmp.requirement_status AS requirement_status,
        cmp.lock_at AS lock_at, cmp.due_at AS due_at, cmp.todo_date AS todo_date,
        submissions.workflow_state AS submission_state, submissions.due_at AS submission_due_at
        FROM "courses"
        INNER JOIN (select *, CAST(req->'id' as bigint) req_id
                    from (
                      select *, jsonb_array_elements(completion_requirements::jsonb) as req
                      from context_modules where canvas_context_type = 'Course' AND canvas_context_id in (#{canvas_course_ids.join(',')})
                    ) as db1
        ) c_r ON c_r.canvas_context_id = courses.canvas_id AND c_r.workflow_state = 'active'
        INNER JOIN context_module_items ON context_module_items.canvas_id = c_r.req_id
        LEFT JOIN context_module_progressions cmp ON cmp.canvas_module_item_id = context_module_items.canvas_id AND cmp.canvas_user_id = #{user_id}
        LEFT JOIN submissions ON submissions.canvas_assignment_id = context_module_items.canvas_assignment_id AND "submissions"."canvas_user_id" = #{user_id}
        WHERE "courses"."workflow_state" IN ('active', 'available')
        AND "context_module_items"."workflow_state" = 'active'
        #{course_filter_sql}
      SQL
    end

    def course_filter_sql
      if course_id.positive?
        <<~SQL.squish
          AND courses.canvas_id = #{course_id}
        SQL
      else
        <<~SQL.squish
          AND 1 = 1
        SQL
      end
    end

    def context_modules_sql
      <<~SQL
        select *, CAST(req->'id' as bigint) req_id
        from (
          select *, jsonb_array_elements(completion_requirements::jsonb) as req
          from context_modules
        ) as db1
      SQL
    end

    def course_sharded_id(canvas_course_id)
      (PandaPal::Organization::SHARD_OFFSET * organization_shard_id) + canvas_course_id.to_i
    end
  end
end
