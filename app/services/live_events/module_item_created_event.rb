# frozen_string_literal: true

module LiveEvents
  class ModuleItemCreatedEvent < LiveEvents::ModuleItemEvent
    def process
      # Ensure context module exists and is synced first
      context_module = ContextModule.find_or_initialize_by(
        canvas_id: local_canvas_id(payload[:module_id]),
        canvas_context_id: local_canvas_id(payload[:context_id]),
        canvas_context_type: payload[:context_type]
      )
      context_module.sync_from_api

      Rails.logger.info("#{event_info} Context module synced: module_id=#{context_module.canvas_id}, " \
                        "context_id=#{context_module.canvas_context_id}")

      context_module_item = ContextModuleItem.find_or_initialize_by(
        canvas_id: local_canvas_id(payload[:module_item_id]),
        canvas_context_module_id: local_canvas_id(payload[:module_id])
      )
      context_module_item.workflow_state = payload[:workflow_state]
      context_module_item.sync_from_api

      Rails.logger.info("#{event_info} Module item created and synced: module_item_id=#{context_module_item.canvas_id}, " \
                        "module_id=#{payload[:module_id]}")
    end

    private

    def event_info
      @event_info ||= "[Org: #{current_organization.name}] [ModuleItemCreatedEvent]"
    end
  end
end
