# frozen_string_literal: true

# # #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#

class ContextModuleItem < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable
  # include CanvasSync::Concerns::LiveEventSync

  canvas_sync_features :defaults

  belongs_to :context_module, primary_key: :canvas_id, foreign_key: :canvas_context_module_id, optional: true
  belongs_to :content, polymorphic: true, optional: true, primary_key: :canvas_id, foreign_key: :canvas_content_id, foreign_type: :canvas_content_type
  belongs_to :assignment, foreign_key: :canvas_assignment_id, primary_key: :canvas_id, optional: true

  api_syncable({
                 canvas_id: :id,
                 canvas_context_module_id: :module_id,
                 position: :position,
                 canvas_content_type: :type,
                 canvas_content_id: :content_id,
                 canvas_assignment_id: :assignment_id,
                 workflow_state: :workflow_state
               }, lambda { |api|
                    module_item = api.module_item(context_module.context.canvas_id, context_module.canvas_id, canvas_id)
                    module_item['assignment_id'] = api.assignment(context_module.context.canvas_id, module_item['content_id'])['id'] if module_item['type'] == 'Assignment'
                    module_item['workflow_state'] = module_item['published'] ? 'active' : 'unpublished'
                    module_item
                  })
end
