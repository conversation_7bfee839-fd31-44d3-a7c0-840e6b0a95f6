# frozen_string_literal: true

context_module = ContextModule.find_or_initialize_by(
  canvas_id: '106643',
  canvas_context_id: '58822',
  canvas_context_type: 'Course'
)
context_module.sync_from_api

context_module = canvas_sync_client.course_module(58_822, 106_643, { include: ['items'] })

context_module = canvas_sync_client.course_modules(58_822, { include: ['items'] }).all_pages!.each do |cm|
  # pp cm[:id].in? [504566, 504575, 504584, 504592, 504596, 504597]
  context_module = ContextModule.find_or_initialize_by(
    canvas_id: cm[:id],
    canvas_context_id: 58_822,
    canvas_context_type: 'Course'
  )
  context_module.sync_from_api
rescue StandardError => e
  puts e
  puts cm
  zcm = ContextModule.find_by_canvas_id cm[:id]
  zcm && zcm.canvas_context_type.nil? && zcm.canvas_context_id.nil?
  zcm.delete
  context_module = ContextModule.find_or_initialize_by(
    canvas_id: cm[:id],
    canvas_context_id: 58_822,
    canvas_context_type: 'Course'
  )
  context_module.sync_from_api
  #         context_module = ContextModule.find_or_initialize_by(
  #   canvas_id: '106643',
  #   canvas_context_id: '58822',
  #   canvas_context_type: 'Course'
  # )
  # context_module.sync_from_api
end
context_module['completion_requirements'] = context_module['items']&.map { |item| { id: item['id'] }.merge(item['completion_requirement']) if item['completion_requirement'] }&.compact
course = Course.find_by_canvas_id(58_822)
course.context_modules.map(&:sync_from_api)
course.context_modules.update_all(workflow_state: 'active')

course.context_modules.where(canvas_id: 106_643).each do |cm|
  # cm.completion_requirements.each do |z|
  # pp z
  vvz = canvas_sync_client.get("/api/v1/courses/#{course.canvas_id}/modules/#{cm.canvas_id}/items").all_pages!
  # pp vvz
  vvz.each do |gfas|
    next unless gfas['id'] == 504_584

    # pp gfas['id']
    cmzc = ContextModuleItem.find_or_initialize_by(canvas_id: gfas['id'], canvas_context_module_id: cm.canvas_id)
    cmzc.sync_from_api
    pp cmzc
  end
  # end
end
nil
