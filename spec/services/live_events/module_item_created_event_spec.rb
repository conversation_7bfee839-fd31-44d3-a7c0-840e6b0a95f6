# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LiveEvents::ModuleItemCreatedEvent, type: :service do
  let(:course) { create(:course, canvas_id: 12) }
  let(:context_module) { create(:context_module, canvas_id: 34, canvas_context_id: course.canvas_id) }

  let(:payload) do
    {
      body: {
        module_id: context_module.canvas_id,
        context_id: course.canvas_id,
        context_type: 'Course',
        module_item_id: 56,
        workflow_state: 'active'
      }
    }.with_indifferent_access
  end

  let(:service) { described_class.new }

  before do
    allow(Rails.logger).to receive(:info)
  end

  describe '#process' do
    let(:context_module_double) { instance_double(ContextModule, sync_from_api: true, canvas_id: 34, canvas_context_id: 12) }
    let(:context_module_item_double) { instance_double(ContextModuleItem, sync_from_api: true, canvas_id: 56) }

    before do
      allow(ContextModule).to receive(:find_or_initialize_by).and_return(context_module_double)
      allow(ContextModuleItem).to receive(:find_or_initialize_by).and_return(context_module_item_double)
    end

    it 'finds or initializes context module with correct attributes' do
      expect(ContextModule).to receive(:find_or_initialize_by).with(
        canvas_id: context_module.canvas_id,
        canvas_context_id: course.canvas_id,
        canvas_context_type: 'Course'
      )

      service.perform(payload)
    end

    it 'syncs context module from API' do
      expect(context_module_double).to receive(:sync_from_api)

      service.perform(payload)
    end

    it 'logs context module sync' do
      expect(Rails.logger).to receive(:info).with(/Context module synced/)

      service.perform(payload)
    end

    it 'finds or initializes context module item with correct attributes' do
      expect(ContextModuleItem).to receive(:find_or_initialize_by).with(
        canvas_id: 56,
        canvas_context_module_id: context_module.canvas_id
      )

      service.perform(payload)
    end

    it 'syncs context module item from API' do
      expect(context_module_item_double).to receive(:sync_from_api)

      service.perform(payload)
    end

    it 'logs module item creation and sync' do
      expect(Rails.logger).to receive(:info).with(/Module item created and synced/)

      service.perform(payload)
    end

    context 'with cross-shard IDs' do
      let(:global_module_id) { 10_000_000_000_034 }
      let(:global_context_id) { 10_000_000_000_012 }
      let(:global_module_item_id) { 10_000_000_000_056 }

      let(:payload) do
        {
          body: {
            module_id: global_module_id,
            context_id: global_context_id,
            context_type: 'Course',
            module_item_id: global_module_item_id,
            workflow_state: 'active'
          }
        }.with_indifferent_access
      end

      it 'converts global IDs to local IDs for context module' do
        expect(ContextModule).to receive(:find_or_initialize_by).with(
          canvas_id: 34, # local ID
          canvas_context_id: 12, # local ID
          canvas_context_type: 'Course'
        )

        service.perform(payload)
      end

      it 'converts global IDs to local IDs for context module item' do
        expect(ContextModuleItem).to receive(:find_or_initialize_by).with(
          canvas_id: 56, # local ID
          canvas_context_module_id: 34 # local ID
        )

        service.perform(payload)
      end
    end
  end

  describe '#event_info' do
    it 'returns formatted event info with organization name' do
      expect(service.send(:event_info)).to eq("[Org: #{service.current_organization.name}] [ModuleItemCreatedEvent]")
    end
  end
end
