# frozen_string_literal: true

class CanvasSyncStarterWorker
  include Sidekiq::Job

  def perform(_opts = {})
    api_acc = canvas_sync_client.account('self', includes: ['global_id'])
    current_organization.tap do |org|
      org.canvas_shard_id = (api_acc[:global_id] / PandaPal::Organization::SHARD_OFFSET).floor
      org.settings[:canvas][:default_time_zone] = api_acc[:default_time_zone]
      org.save!
    end

    last_enqueued_batch = CanvasSync::SyncBatch.where(status: 'processing', batch_genre: 'default').last

    # Refuse to run syncs of the same genre if there is a running batch sync
    if last_enqueued_batch&.status == 'processing'
      Rails.logger.warn('Attempted to start a SyncBatch while a previous batch sync is still processing.')
      return
    end

    models = %w[roles accounts courses users pseudonyms enrollments assignments user_observers context_module_items]
    job_chain = CanvasSync.default_provisioning_report_chain(
      models,
      options: {},
      term_scope: 'active',
      updated_after: Rails.env.development? ? false : nil
    )

    job_chain.insert({ job: CanvasSync::Jobs::SyncSubmissionsJob, options: { report_params: { include_all_except: ['deleted'] } } })

    job_chain.insert({ job: SyncContextModuleJob, options: {} })
    job_chain.insert({ job: SyncScoresJob, options: {} })
    job_chain.insert({ job: PullCourseImageJob, options: { updated_after: true } })
    job_chain.insert({ job: SyncGradingSchemeJob, options: {} })
    job_chain.insert({ job: GenerateAccountGradingSchemeColorsJob, options: {} })
    job_chain.insert({ job: SyncCalendarEventJob, options: {} })
    job_chain.insert({ job: RefreshCrossShardUsersJob })

    job_chain.insert({ job: SyncContextModuleProgressJob, after: 'CanvasSync::Jobs::SyncContextModuleItemsJob', options: {} })

    job_chain.process!
  end
end
