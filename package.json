{"name": "stride-k5-dashboard", "private": true, "engines": {"node": "20.x"}, "dependencies": {"@babel/core": "7", "@babel/plugin-transform-runtime": "7", "@babel/preset-env": "7", "@babel/preset-react": "^7.25.9", "@babel/runtime": "7", "@instructure/ui-alerts": "^10.23.0", "@instructure/ui-avatar": "^10.23.0", "@instructure/ui-badge": "^10.23.0", "@instructure/ui-breadcrumb": "^10.23.0", "@instructure/ui-buttons": "^10.23.0", "@instructure/ui-checkbox": "^10.23.0", "@instructure/ui-color-picker": "^10.23.0", "@instructure/ui-date-input": "^10.23.0", "@instructure/ui-flex": "^10.23.0", "@instructure/ui-grid": "^10.23.0", "@instructure/ui-heading": "^10.23.0", "@instructure/ui-icons": "^10.23.0", "@instructure/ui-img": "^10.23.0", "@instructure/ui-link": "^10.23.0", "@instructure/ui-metric": "^10.23.0", "@instructure/ui-modal": "^10.23.0", "@instructure/ui-pill": "^10.23.0", "@instructure/ui-popover": "^10.23.0", "@instructure/ui-progress": "^10.23.0", "@instructure/ui-radio-input": "^10.23.0", "@instructure/ui-rating": "^10.23.0", "@instructure/ui-responsive": "^10.23.0", "@instructure/ui-select": "^10.23.0", "@instructure/ui-spinner": "^10.23.0", "@instructure/ui-table": "^10.23.0", "@instructure/ui-tabs": "^10.23.0", "@instructure/ui-text": "^10.23.0", "@instructure/ui-toggle-details": "^10.23.0", "@instructure/ui-view": "^10.23.0", "@lottiefiles/react-lottie-player": "^3.6.0", "@types/babel__core": "7", "@types/webpack": "5", "axios": "^1.7.7", "babel-loader": "8", "compression-webpack-plugin": "9", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "lottie-react": "^2.4.1", "luxon": "^3.6.1", "mini-css-extract-plugin": "^2.9.1", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^6.28.0", "react-router-dom": "^6.28.0", "react_ujs": "^3.2.1", "shakapacker": "7.2.3", "style-loader": "^4.0.0", "terser-webpack-plugin": "5", "webpack": "5", "webpack-assets-manifest": "5", "webpack-cli": "4", "webpack-merge": "5"}, "version": "0.1.0", "babel": {"presets": ["./node_modules/shakapacker/package/babel/preset.js", "@babel/preset-react"]}, "browserslist": ["defaults"], "devDependencies": {"webpack-dev-server": "4"}}